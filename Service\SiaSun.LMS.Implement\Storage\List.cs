﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Data;

namespace SiaSun.LMS.Implement.Storage
{
    /// <summary>
    /// 拣选工作站相关
    /// doing
    /// </summary>
    public class List : S_BaseService
    {
        /// <summary>
        /// 拣选工作站相关
        /// 根据拣选计划进行库存锁定
        /// done
        /// </summary>
        /// <param name="PLAN_LIST_ID"></param>
        /// <param name="GOODS_ID"></param>
        /// <param name="PLAN_LIST_QUANTITY"></param>
        /// <param name="LOCK_REMARK"></param>
        /// <param name="STORAGE_LIST_QUANTITY_LOCK">锁定的库存数量</param>
        /// <param name="sResult"></param>
        /// <returns></returns>
        public bool Lock(int PLAN_LIST_ID,
                   int GOODS_ID,
                   decimal PLAN_LIST_QUANTITY,
                   string LOCK_REMARK,
                   out decimal STORAGE_LIST_QUANTITY_LOCK,
                   out string sResult)
        {
            bool bResult = true;
            sResult = string.Empty;
            STORAGE_LIST_QUANTITY_LOCK = 0;

            string sMessage = string.Empty;
            Model.GOODS_MAIN mGOODS_MAIN = this._P_GOODS_MAIN.GetModel(GOODS_ID);
            bResult = !(mGOODS_MAIN == null);
            if (!bResult)
            {
                sResult = string.Format("未找到物料信息 ID:{0}", GOODS_ID);
                return bResult;
            }
            while (PLAN_LIST_QUANTITY > 0)
            {

                #region while
                Storage.List mStorageList = new Storage.List();
                decimal STORAGE_LIST_QUANTITY = 0;
                int STORAGE_LIST_ID = 0;

                bool bHave = this.Get(PLAN_LIST_ID, GOODS_ID, out STORAGE_LIST_ID, out STORAGE_LIST_QUANTITY, out sResult);

                if (!bHave)
                {
                    sMessage += string.Format("物料{0}实际数量{1}<计划数量{2}", mGOODS_MAIN.GOODS_CODE, STORAGE_LIST_QUANTITY, PLAN_LIST_QUANTITY);
                    sResult += sMessage;
                    bResult = false;
                }

                if (!bResult)
                {
                    return bResult;
                }

                Model.STORAGE_LIST mSTORAGE_LIST = this._P_STORAGE_LIST.GetModel(STORAGE_LIST_ID);

                Model.STORAGE_LOCK mSTORAGE_LOCK = new Model.STORAGE_LOCK();
                mSTORAGE_LOCK.STORAGE_LIST_ID = STORAGE_LIST_ID;
                mSTORAGE_LOCK.PLAN_LIST_ID = PLAN_LIST_ID;
                mSTORAGE_LOCK.STORAGE_LOCK_FLAG = Enum.FLAG.Disable.ToString("d");//锁定未生成拣选
                mSTORAGE_LOCK.DETAIL_FLAG = Enum.FLAG.Disable.ToString("d");

                mSTORAGE_LOCK.STORAGE_LOCK_REMARK = LOCK_REMARK;
                //库存是否等于需求
                if (PLAN_LIST_QUANTITY >= STORAGE_LIST_QUANTITY)
                {
                    mSTORAGE_LOCK.STORAGE_LOCK_QUANTITY = STORAGE_LIST_QUANTITY;
                }
                else
                {
                    mSTORAGE_LOCK.STORAGE_LOCK_QUANTITY = PLAN_LIST_QUANTITY;
                }

                this._P_STORAGE_LOCK.Add(mSTORAGE_LOCK);

                STORAGE_LIST_QUANTITY_LOCK += mSTORAGE_LOCK.STORAGE_LOCK_QUANTITY;

                PLAN_LIST_QUANTITY -= mSTORAGE_LOCK.STORAGE_LOCK_QUANTITY;

                #endregion
            }

            return bResult;
        }

        /// <summary>
        /// 拣选工作站相关
        /// 根据拣选计划进行库存锁定
        /// done
        /// </summary>
        /// <param name="PLAN_LIST_ID"></param>
        /// <param name="GOODS_ID"></param>
        /// <param name="PLAN_LIST_QUANTITY"></param>
        /// <param name="LOCK_REMARK"></param>
        /// <param name="STORAGE_LIST_QUANTITY_LOCK">锁定的库存数量</param>
        /// <param name="sResult"></param>
        /// <returns></returns>
        public bool LockPartly(int PLAN_LIST_ID,
                   int GOODS_ID,
                   decimal PLAN_LIST_QUANTITY,
                   string LOCK_REMARK,
                   out decimal STORAGE_LIST_QUANTITY_LOCK,
                   out string sResult)
        {
            bool bResult = true;
            sResult = string.Empty;
            STORAGE_LIST_QUANTITY_LOCK = 0;

            string sMessage = string.Empty;
            Model.GOODS_MAIN mGOODS_MAIN = this._P_GOODS_MAIN.GetModel(GOODS_ID);
            bResult = !(mGOODS_MAIN == null);
            if (!bResult)
            {
                sResult = string.Format("未找到物料信息 ID:{0}", GOODS_ID);
                return bResult;
            }
            while (PLAN_LIST_QUANTITY > 0)
            {

                #region while
                Storage.List mStorageList = new Storage.List();
                decimal STORAGE_LIST_QUANTITY = 0;
                int STORAGE_LIST_ID = 0;

                bool bHave = this.Get(PLAN_LIST_ID, GOODS_ID, out STORAGE_LIST_ID, out STORAGE_LIST_QUANTITY, out sResult);

                if (!bHave)
                {
                    //部分锁定
                    sMessage += string.Format("物料{0}实际数量{1}<计划数量{2}", mGOODS_MAIN.GOODS_CODE, STORAGE_LIST_QUANTITY, PLAN_LIST_QUANTITY);
                    sResult += sMessage;
                    break;
                    //bResult = false;
                }

                if (!bResult)
                {
                    return bResult;
                }

                Model.STORAGE_LIST mSTORAGE_LIST = this._P_STORAGE_LIST.GetModel(STORAGE_LIST_ID);

                Model.STORAGE_LOCK mSTORAGE_LOCK = new Model.STORAGE_LOCK();
                mSTORAGE_LOCK.STORAGE_LIST_ID = STORAGE_LIST_ID;
                mSTORAGE_LOCK.PLAN_LIST_ID = PLAN_LIST_ID;
                mSTORAGE_LOCK.STORAGE_LOCK_FLAG = Enum.FLAG.Disable.ToString("d");//锁定未生成拣选
                mSTORAGE_LOCK.DETAIL_FLAG = Enum.FLAG.Disable.ToString("d");

                mSTORAGE_LOCK.STORAGE_LOCK_REMARK = LOCK_REMARK;
                //库存是否等于需求
                if (PLAN_LIST_QUANTITY >= STORAGE_LIST_QUANTITY)
                {
                    mSTORAGE_LOCK.STORAGE_LOCK_QUANTITY = STORAGE_LIST_QUANTITY;
                }
                else
                {
                    mSTORAGE_LOCK.STORAGE_LOCK_QUANTITY = PLAN_LIST_QUANTITY;
                }

                this._P_STORAGE_LOCK.Add(mSTORAGE_LOCK);

                STORAGE_LIST_QUANTITY_LOCK += mSTORAGE_LOCK.STORAGE_LOCK_QUANTITY;

                PLAN_LIST_QUANTITY -= mSTORAGE_LOCK.STORAGE_LOCK_QUANTITY;

                #endregion
            }

            return bResult;
        }

        /// <summary>
        /// 拣选工作站相关
        /// 获取满足WBS计划明细需求的库存及数量
        /// 2018-4-17 通过现场反馈，原程序有如下问题：
        /// **特别重要** 拣选工作站订单锁定出库库存时，
        /// 在plan_list的锁定方法中，必须要判断如下：不同拣选工作站的订单不能锁定在同一个原料箱中，
        /// 即某一个拣选工作站的订单执行库存锁定时，应完全绕开已经有物料被其他拣选工作站的订单锁定的原料箱，
        /// 拣选工作站（1，2，3）的订单锁定库存时，应该做到原料箱（storage_main）层的隔离。
        /// testing
        /// </summary>
        /// <param name="PLAN_LIST_ID"></param>
        /// <param name="GOODS_ID"></param>
        /// <param name="STORAGE_LIST_ID">找到的库存明细ID</param>
        /// <param name="STORAGE_LIST_QUANTITY">找到的库存数量</param>
        /// <param name="sResult"></param>
        /// <returns></returns>
        public bool Get(int PLAN_LIST_ID,
                          int GOODS_ID,
                          out int STORAGE_LIST_ID,
                          out decimal STORAGE_LIST_QUANTITY,
                          out string sResult)
        {
            bool bResult = false;
            sResult = string.Empty;
            STORAGE_LIST_ID = 0;
            STORAGE_LIST_QUANTITY = 0;

            int iPickStationPlcNo = 0;
            string sAreaIdOrder = "ASC";

            //2019-07-06
            //通过PLAN_LIST_ID确定绑定的拣选工作站所在的库区
            //更改库区排序的正序倒序
            #region  二期实施
            DataTable PickStationDt =
                this.GetList(string.Format("SELECT PROPERTY1 FROM V_PICK_POSITION_BIND WHERE PLAN_LIST_ID={0}",
                PLAN_LIST_ID));
            bResult = PickStationDt.Rows.Count > 0;
            if (!bResult)
            {
                sResult = string.Format("未找到计划明细{0}所绑定的拣选工作站对应库区", PLAN_LIST_ID);
                return bResult;
            }

            bResult = int.TryParse(PickStationDt.Rows[0]["PROPERTY1"].ToString(), out iPickStationPlcNo);

            if (!bResult)
            {
                sResult = string.Format("计划明细{0}绑定的拣选工作站PlcNo:{1} 无法转换",
                    PLAN_LIST_ID, PickStationDt.Rows[0]["PROPERTY1"]);
                return bResult;
            }

            // 拣选工作站1-3号对应1期库区 area_id=1
            // 拣选工作站4-6号对应2期库区 area_id=3
            if (iPickStationPlcNo > 3)
            {
                sAreaIdOrder = "DESC";
            }

            #endregion
            #region old
            //string sSQL = string.Format(@"SELECT STORAGE_LIST_ID,STORAGE_LIST_QUANTITY,STORAGE_LOCK_QUANTITY
            //             FROM  V_STORAGE_LIST_LOCK
            //             WHERE GOODS_ID = {0}
            //             AND   STORAGE_LIST_QUANTITY > STORAGE_LOCK_QUANTITY
            //             AND CELL_TYPE='Cell' 
            //             AND GOODS_PROPERTY6 is null 
            //             AND GOODS_PROPERTY7 is null 
            //             AND GOODS_PROPERTY8 is null 
            //             AND rownum<2
            //          ORDER BY ENTRY_TIME",
            //          /**/ GOODS_ID);
            #endregion

            //2018-4-17 确保不同拣选工作站的订单不能同锁一个箱子（storage_main）
            #region new
            //string sSQL = string.Format(@"SELECT STORAGE_LIST_ID,STORAGE_LIST_QUANTITY,STORAGE_LOCK_QUANTITY
            //             FROM  V_STORAGE_LIST_LOCK
            //             WHERE GOODS_ID = {0}
            //             AND   STORAGE_LIST_QUANTITY > STORAGE_LOCK_QUANTITY
            //             AND CELL_TYPE='Cell' 
            //             AND GOODS_PROPERTY6 is null 
            //             AND GOODS_PROPERTY7 is null 
            //             AND GOODS_PROPERTY8 is null
            //             AND STORAGE_ID NOT IN 
            //              (SELECT STORAGE_ID FROM STORAGE_LIST WHERE STORAGE_LIST_ID IN
            //                (SELECT STORAGE_LIST_ID FROM STORAGE_LOCK WHERE PLAN_LIST_ID IN
            //                  (SELECT PLAN_LIST_ID FROM PLAN_LIST WHERE PLAN_ID IN 
            //                    (SELECT PLAN_ID FROM PLAN_MAIN WHERE PLAN_GROUP IN
            //                      (SELECT PLAN_GROUP_CODE FROM T_PICK_STATION WHERE PLAN_GROUP_CODE NOT IN 
            //                        (SELECT PLAN_GROUP FROM PLAN_MAIN WHERE PLAN_ID IN
            //                          (SELECT PLAN_ID FROM PLAN_LIST WHERE PLAN_LIST_ID={1})
            //                         )
            //                       )
            //                     )
            //                   )
            //                 )
            //               ) 
            //             AND rownum<2
            //          ORDER BY ENTRY_TIME",
            //          /**/ GOODS_ID,
            //          /**/ PLAN_LIST_ID);

            #endregion
            //新增条件，如果选定的库存所在货位到拣选工作站位置路径不可用，依然不能锁定，增加了路径可用性检查
            //在现场进行提交
            //hejiaji 20180508 
            //提交测试 20180606
            #region update
            //string sSQL = string.Format(@"SELECT STORAGE_LIST_ID,STORAGE_LIST_QUANTITY,STORAGE_LOCK_QUANTITY
            //             FROM  V_STORAGE_LIST_LOCK 
            //             WHERE GOODS_ID = {0}
            //             AND   STORAGE_LIST_QUANTITY > STORAGE_LOCK_QUANTITY
            //             AND CELL_TYPE='Cell' 
            //             AND GOODS_PROPERTY6 is null 
            //             AND GOODS_PROPERTY7 is null 
            //             AND GOODS_PROPERTY8 is null
            //             AND STORAGE_LIST_ID NOT IN (SELECT STORAGE_LIST_ID FROM MANAGE_LIST)
            //             AND STORAGE_ID NOT IN 
            //              (SELECT STORAGE_ID FROM STORAGE_LIST WHERE STORAGE_LIST_ID IN
            //                (SELECT STORAGE_LIST_ID FROM STORAGE_LOCK WHERE PLAN_LIST_ID IN
            //                  (SELECT PLAN_LIST_ID FROM PLAN_LIST WHERE PLAN_ID IN 
            //                    (SELECT PLAN_ID FROM PLAN_MAIN WHERE PLAN_GROUP IN
            //                      (SELECT PLAN_GROUP_CODE FROM T_PICK_STATION WHERE PLAN_GROUP_CODE NOT IN 
            //                        (SELECT PLAN_GROUP FROM PLAN_MAIN WHERE PLAN_ID IN
            //                          (SELECT PLAN_ID FROM PLAN_LIST WHERE PLAN_LIST_ID={1})
            //                         )
            //                       )
            //                     )
            //                   )
            //                 )
            //               ) 
            //             AND rownum<2
            //             AND (SELECT CONTROL_ROUTE_STATUS from IO_CONTROL_ROUTE 
            //             where START_DEVICE=V_STORAGE_LIST_LOCK.DEVICE_CODE 
            //             and END_DEVICE in (SELECT DEVICE_CODE FROM WH_CELL WHERE CELL_ID 
            //             IN (SELECT WH_CELL_ID FROM T_PICK_STATION WHERE STATION_ID IN 
            //             (SELECT PICK_STATION_ID FROM T_PICK_POSITION_PLAN_BIND WHERE PLAN_ID IN
            //             ( SELECT PLAN_ID FROM PLAN_MAIN WHERE PLAN_ID IN 
            //             (SELECT PLAN_ID FROM PLAN_LIST WHERE PLAN_LIST_ID={1}))))))='1'
            //          ORDER BY ENTRY_TIME",
            //          /**/ GOODS_ID,
            //          /**/ PLAN_LIST_ID);

            #endregion


            //hejiaji 20180731
            //新增条件 goods_property2不能等于1，代表异常库存，即实物与数量不符的库存,不能锁定
            #region update20180731
            //string sSQL = string.Format(@"SELECT STORAGE_LIST_ID,STORAGE_LIST_QUANTITY,STORAGE_LOCK_QUANTITY
            //             FROM  V_STORAGE_LIST_LOCK 
            //             WHERE GOODS_ID = {0}
            //             AND   STORAGE_LIST_QUANTITY > STORAGE_LOCK_QUANTITY
            //             AND CELL_TYPE='Cell'
            //             AND (GOODS_PROPERTY2 is null or GOODS_PROPERTY2 !='1')
            //             AND GOODS_PROPERTY6 is null 
            //             AND GOODS_PROPERTY7 is null 
            //             AND GOODS_PROPERTY8 is null
            //             AND STORAGE_LIST_ID NOT IN (SELECT STORAGE_LIST_ID FROM MANAGE_LIST)
            //             AND STORAGE_ID NOT IN 
            //              (SELECT STORAGE_ID FROM STORAGE_LIST WHERE STORAGE_LIST_ID IN
            //                (SELECT STORAGE_LIST_ID FROM STORAGE_LOCK WHERE PLAN_LIST_ID IN
            //                  (SELECT PLAN_LIST_ID FROM PLAN_LIST WHERE PLAN_ID IN 
            //                    (SELECT PLAN_ID FROM PLAN_MAIN WHERE PLAN_GROUP IN
            //                      (SELECT PLAN_GROUP_CODE FROM T_PICK_STATION WHERE PLAN_GROUP_CODE NOT IN 
            //                        (SELECT PLAN_GROUP FROM PLAN_MAIN WHERE PLAN_ID IN
            //                          (SELECT PLAN_ID FROM PLAN_LIST WHERE PLAN_LIST_ID={1})
            //                         )
            //                       )
            //                     )
            //                   )
            //                 )
            //               ) 
            //             AND rownum<2
            //             AND (SELECT CONTROL_ROUTE_STATUS from IO_CONTROL_ROUTE 
            //             where START_DEVICE=V_STORAGE_LIST_LOCK.DEVICE_CODE 
            //             and END_DEVICE in (SELECT DEVICE_CODE FROM WH_CELL WHERE CELL_ID 
            //             IN (SELECT WH_CELL_ID FROM T_PICK_STATION WHERE STATION_ID IN 
            //             (SELECT PICK_STATION_ID FROM T_PICK_POSITION_PLAN_BIND WHERE PLAN_ID IN
            //             ( SELECT PLAN_ID FROM PLAN_MAIN WHERE PLAN_ID IN 
            //             (SELECT PLAN_ID FROM PLAN_LIST WHERE PLAN_LIST_ID={1}))))))='1'
            //          ORDER BY ENTRY_TIME",
            //          /**/ GOODS_ID,
            //          /**/ PLAN_LIST_ID);
            #endregion

            //hejiaji 20201027
            //先判断物料的入库时间，如果在一段时间（设定）之上，则不考虑就近库区优先出库
            #region 20201027先判断物料入库时间，在时间段内才优先就近库区锁定出库
            string PickStationStorageEntryTimeRange = string.Empty;
            bResult = _S_SystemService.GetSysParameter("PickStationStorageEntryTimeRange", out PickStationStorageEntryTimeRange);
            if (!bResult)
            {
                sResult = string.Format("获取参数错误，无法获得PickStationStorageEntryTimeRange参数");
                return bResult;
            }
            int _intPickStationStorageEntryTimeRange = 0;
            bResult = int.TryParse(PickStationStorageEntryTimeRange, out _intPickStationStorageEntryTimeRange);
            if (!bResult)
            {
                sResult = string.Format("获取参数错误，获得PickStationStorageEntryTimeRange参数{0}无法转换为整数", PickStationStorageEntryTimeRange);
                return bResult;
            }

            DateTime checkDateTime = DateTime.Now.AddDays((0 - _intPickStationStorageEntryTimeRange));

            string checkDateTimeStr = checkDateTime.ToString("yyyy-MM-dd HH:mm:ss");


            string sCheckSQL = string.Format(@"SELECT STORAGE_LIST_ID,STORAGE_LIST_QUANTITY,STORAGE_LOCK_QUANTITY
                         FROM  V_STORAGE_LIST_LOCK 
                         WHERE GOODS_ID = {0}
                         AND   STORAGE_LIST_QUANTITY > STORAGE_LOCK_QUANTITY
                         AND CELL_TYPE='Cell'
                         AND (GOODS_PROPERTY2 is null or GOODS_PROPERTY2 !='1')
                         AND GOODS_PROPERTY6 is null 
                         AND GOODS_PROPERTY7 is null 
                         AND GOODS_PROPERTY8 is null
                         AND ENTRY_TIME < '{3}'
                         AND V_STORAGE_LIST_LOCK.STOCK_BARCODE NOT IN (SELECT STOCK_BARCODE FROM MANAGE_MAIN)
                         AND STORAGE_LIST_ID NOT IN (SELECT STORAGE_LIST_ID FROM MANAGE_LIST)
                         AND STORAGE_ID NOT IN 
                          (SELECT STORAGE_ID FROM STORAGE_LIST WHERE STORAGE_LIST_ID IN
                            (SELECT STORAGE_LIST_ID FROM STORAGE_LOCK WHERE PLAN_LIST_ID IN
                              (SELECT PLAN_LIST_ID FROM PLAN_LIST WHERE PLAN_ID IN 
                                (SELECT PLAN_ID FROM PLAN_MAIN WHERE PLAN_GROUP IN
                                  (SELECT PLAN_GROUP_CODE FROM T_PICK_STATION WHERE PLAN_GROUP_CODE NOT IN 
                                    (SELECT PLAN_GROUP FROM PLAN_MAIN WHERE PLAN_ID IN
                                      (SELECT PLAN_ID FROM PLAN_LIST WHERE PLAN_LIST_ID={1})
                                     )
                                   )
                                 )
                               )
                             )
                           ) 
AND STORAGE_ID NOT IN 
                          (SELECT STORAGE_ID FROM STORAGE_LIST WHERE STORAGE_LIST_ID IN
                            (SELECT STORAGE_LIST_ID FROM STORAGE_LOCK WHERE PLAN_LIST_ID IN
                              (SELECT PLAN_LIST_ID FROM PLAN_LIST WHERE PLAN_ID IN 
                                (SELECT PLAN_ID FROM PLAN_MAIN WHERE PLAN_GROUP IN
                                  (SELECT POSITION_CODE FROM T_PICK_POSITION WHERE POSITION_CODE NOT IN 
                                    (SELECT PLAN_GROUP FROM PLAN_MAIN WHERE PLAN_ID IN
                                      (SELECT PLAN_ID FROM PLAN_LIST WHERE PLAN_LIST_ID={1})
                                     )
                                   )
                                 )
                               )
                             )
                           ) 
                         AND rownum<2
                         AND (SELECT CONTROL_ROUTE_STATUS from IO_CONTROL_ROUTE 
                         where START_DEVICE=V_STORAGE_LIST_LOCK.DEVICE_CODE 
                         and END_DEVICE in (SELECT DEVICE_CODE FROM WH_CELL WHERE CELL_ID 
                         IN (SELECT WH_CELL_ID FROM T_PICK_STATION WHERE STATION_ID IN 
                         (SELECT PICK_STATION_ID FROM T_PICK_POSITION_PLAN_BIND WHERE PLAN_ID IN
                         ( SELECT PLAN_ID FROM PLAN_MAIN WHERE PLAN_ID IN 
                         (SELECT PLAN_ID FROM PLAN_LIST WHERE PLAN_LIST_ID={1}))))))='1'
                      ORDER BY AREA_ID {2}, ENTRY_TIME",
                      /**/ GOODS_ID,
                      /**/ PLAN_LIST_ID,
                      /**/ sAreaIdOrder,
                      /**/ checkDateTimeStr);

            DataTable dtCheck = this.GetList(sCheckSQL);
            bResult = dtCheck.Rows.Count > 0;

            if (bResult)
            {
                STORAGE_LIST_ID = Convert.ToInt32(dtCheck.Rows[0]["STORAGE_LIST_ID"]);
                STORAGE_LIST_QUANTITY = Convert.ToDecimal(dtCheck.Rows[0]["STORAGE_LIST_QUANTITY"]) - Convert.ToDecimal(dtCheck.Rows[0]["STORAGE_LOCK_QUANTITY"]);
                return bResult;
            }

            #endregion

            //修改V_STORAGE_LIST_LOCK视图，增加了AREA_ID字段
            //根据拣选工作站所在库区，确定AREA_ID排序
            #region 20190706 二期
            string sSQL = string.Format(@"SELECT STORAGE_LIST_ID,STORAGE_LIST_QUANTITY,STORAGE_LOCK_QUANTITY
                         FROM  V_STORAGE_LIST_LOCK 
                         WHERE GOODS_ID = {0}
                         AND   STORAGE_LIST_QUANTITY > STORAGE_LOCK_QUANTITY
                         AND CELL_TYPE='Cell'
                         AND (GOODS_PROPERTY2 is null or GOODS_PROPERTY2 !='1')
                         AND GOODS_PROPERTY6 is null 
                         AND GOODS_PROPERTY7 is null 
                         AND GOODS_PROPERTY8 is null
                         AND V_STORAGE_LIST_LOCK.STOCK_BARCODE NOT IN (SELECT STOCK_BARCODE FROM MANAGE_MAIN)
                         AND STORAGE_LIST_ID NOT IN (SELECT STORAGE_LIST_ID FROM MANAGE_LIST)
                         AND STORAGE_ID NOT IN 
                          (SELECT STORAGE_ID FROM STORAGE_LIST WHERE STORAGE_LIST_ID IN
                            (SELECT STORAGE_LIST_ID FROM STORAGE_LOCK WHERE PLAN_LIST_ID IN
                              (SELECT PLAN_LIST_ID FROM PLAN_LIST WHERE PLAN_ID IN 
                                (SELECT PLAN_ID FROM PLAN_MAIN WHERE PLAN_GROUP IN
                                  (SELECT PLAN_GROUP_CODE FROM T_PICK_STATION WHERE PLAN_GROUP_CODE NOT IN 
                                    (SELECT PLAN_GROUP FROM PLAN_MAIN WHERE PLAN_ID IN
                                      (SELECT PLAN_ID FROM PLAN_LIST WHERE PLAN_LIST_ID={1})
                                     )
                                   )
                                 )
                               )
                             )
                           ) 
AND STORAGE_ID NOT IN 
                          (SELECT STORAGE_ID FROM STORAGE_LIST WHERE STORAGE_LIST_ID IN
                            (SELECT STORAGE_LIST_ID FROM STORAGE_LOCK WHERE PLAN_LIST_ID IN
                              (SELECT PLAN_LIST_ID FROM PLAN_LIST WHERE PLAN_ID IN 
                                (SELECT PLAN_ID FROM PLAN_MAIN WHERE PLAN_GROUP IN
                                  (SELECT POSITION_CODE FROM T_PICK_POSITION WHERE POSITION_CODE NOT IN 
                                    (SELECT PLAN_GROUP FROM PLAN_MAIN WHERE PLAN_ID IN
                                      (SELECT PLAN_ID FROM PLAN_LIST WHERE PLAN_LIST_ID={1})
                                     )
                                   )
                                 )
                               )
                             )
                           )
                         AND rownum<2
                         AND (SELECT CONTROL_ROUTE_STATUS from IO_CONTROL_ROUTE 
                         where START_DEVICE=V_STORAGE_LIST_LOCK.DEVICE_CODE 
                         and END_DEVICE in (SELECT DEVICE_CODE FROM WH_CELL WHERE CELL_ID 
                         IN (SELECT WH_CELL_ID FROM T_PICK_STATION WHERE STATION_ID IN 
                         (SELECT PICK_STATION_ID FROM T_PICK_POSITION_PLAN_BIND WHERE PLAN_ID IN
                         ( SELECT PLAN_ID FROM PLAN_MAIN WHERE PLAN_ID IN 
                         (SELECT PLAN_ID FROM PLAN_LIST WHERE PLAN_LIST_ID={1}))))))='1'
                      ORDER BY AREA_ID {2}, ENTRY_TIME",
                      /**/ GOODS_ID,
                      /**/ PLAN_LIST_ID,
                      /**/ sAreaIdOrder);
            #endregion
            DataTable dt = this.GetList(sSQL);

            bResult = dt.Rows.Count > 0;

            if (bResult)
            {
                STORAGE_LIST_ID = Convert.ToInt32(dt.Rows[0]["STORAGE_LIST_ID"]);
                STORAGE_LIST_QUANTITY = Convert.ToDecimal(dt.Rows[0]["STORAGE_LIST_QUANTITY"]) - Convert.ToDecimal(dt.Rows[0]["STORAGE_LOCK_QUANTITY"]);
            }

            return bResult;
        }



        /// <summary>
        /// 合并程序修改
        /// 拣选
        /// </summary>
        /// <param name="mMANAGE_MAIN">任务主表</param>
        /// <param name="mMANAGE_MAIN">任务关联表</param>
        /// <param name="lsMANAGE_LIST">任务列表</param>
        /// <param name="lsMANAGE_DETAIL">任务明细</param>
        /// <param name="sResult">返回原因</param>
        /// <returns>返回结果</returns>
        public bool Pick(Model.MANAGE_MAIN mMANAGE_MAIN
            , List<Model.MANAGE_LIST> lsMANAGE_LIST
            , List<Model.MANAGE_DETAIL> lsMANAGE_DETAIL
            , out string sResult)
        {
            bool bResult = true;
            sResult = string.Empty;

            try
            {

                foreach (Model.MANAGE_LIST mMANAGE_LIST in lsMANAGE_LIST)
                {
                    //删除或修改锁定表数量
                    Model.STORAGE_LOCK mSTORAGE_LOCK = this._P_STORAGE_LOCK.GetModel(mMANAGE_LIST.STORAGE_LOCK_ID);
                    bResult = null != mSTORAGE_LOCK;
                    if (!bResult)
                    {
                        sResult = string.Format("未找到锁定列表{0}", mMANAGE_LIST.STORAGE_LOCK_ID);
                        return bResult;
                    }

                    this._log.DebugFormat(@"[拣选工作站] 拣选任务完成时，单据列表{0}锁定列表{1}库存列表{2}锁定数量{3}单据数量{4},条码{5}"
                            , mMANAGE_LIST.PLAN_LIST_ID
                            , mMANAGE_LIST.STORAGE_LOCK_ID
                            , mMANAGE_LIST.STORAGE_LIST_ID
                            , mSTORAGE_LOCK.STORAGE_LOCK_QUANTITY
                            , mMANAGE_LIST.MANAGE_LIST_QUANTITY
                            , mMANAGE_MAIN.STOCK_BARCODE);

                    if (mSTORAGE_LOCK.STORAGE_LOCK_QUANTITY.Equals(mMANAGE_LIST.MANAGE_LIST_QUANTITY))
                    {
                        this._P_STORAGE_LOCK.Delete(mMANAGE_LIST.STORAGE_LOCK_ID);
                    }
                    else
                    {
                        mSTORAGE_LOCK.STORAGE_LOCK_QUANTITY -= mMANAGE_LIST.MANAGE_LIST_QUANTITY;
                        this._P_STORAGE_LOCK.Update(mSTORAGE_LOCK);
                    }

                    //库存列表
                    Model.STORAGE_LIST mSTORAGE_LIST = this._P_STORAGE_LIST.GetModel(mMANAGE_LIST.STORAGE_LIST_ID);

                    if (mSTORAGE_LIST == null)
                    {
                        bResult = false;
                        sResult = string.Format("未找到库存列表{0}", mMANAGE_LIST.STORAGE_LIST_ID);
                        return bResult;
                    }

                    //库存主表
                    Model.STORAGE_MAIN mSTORAGE_MAIN = this._P_STORAGE_MAIN.GetModel(mSTORAGE_LIST.STORAGE_ID);
                    if (mSTORAGE_MAIN == null)
                    {
                        bResult = false;
                        sResult = string.Format("未找到库存主表{0}", mSTORAGE_LIST.STORAGE_ID);
                        return bResult;
                    }

                    Model.STORAGE_MAIN mSTORAGE_MAIN_NEW = this._P_STORAGE_MAIN.GetModelCellID(mMANAGE_MAIN.END_CELL_ID);

                    if (null == mSTORAGE_MAIN_NEW)
                    {
                        bResult = false;
                        sResult = string.Format("目标位置{0}没有箱库存", mMANAGE_MAIN.END_CELL_ID);
                        return bResult;
                    }

                    //预绑定判断齐套箱不能放置多个WBS的物料
                    Model.PLAN_LIST cpPLAN_LIST = this._P_PLAN_LIST.GetModel(mMANAGE_LIST.PLAN_LIST_ID);
                    if (cpPLAN_LIST == null)
                    {
                        bResult = false;
                        sResult = string.Format("未找到计划PLAN_LIST:{0}", mMANAGE_LIST.PLAN_LIST_ID);
                        return bResult;
                    }

                    Model.PLAN_MAIN cpPLAN_MAIN = this._P_PLAN_MAIN.GetModel(cpPLAN_LIST.PLAN_ID);
                    if (cpPLAN_LIST == null)
                    {
                        bResult = false;
                        sResult = string.Format("未找到计划PLAN_MAIN:{0}", cpPLAN_LIST.PLAN_ID);
                        return bResult;
                    }
                    DataTable cpDT = this.GetList(string.Format(@"select distinct plan_id  from (select * from plan_list where plan_list_id in (select plan_list_id from storage_list where storage_id ={0}))", mSTORAGE_MAIN_NEW.STORAGE_ID));
                    if (cpDT != null)
                    {
                        if (cpDT.Rows.Count > 0)
                        {
                            List<int> cp_plan_id_list = new List<int>();
                            foreach (DataRow drr in cpDT.Rows)
                            {
                                cp_plan_id_list.Add(Convert.ToInt32(drr[0].ToString()));
                            }

                            if (!cp_plan_id_list.Contains(cpPLAN_MAIN.PLAN_ID))
                            {
                                bResult = false;
                                sResult = string.Format("预绑定相关错误 不同WBS所属库存不能放置在同一个齐套箱");
                                return bResult;
                            }
                        }
                    }
                    //预绑定

                    mSTORAGE_MAIN_NEW.CELL_MODEL = Enum.CellModel.PickingBox.ToString("d");
                    this._P_STORAGE_MAIN.Update(mSTORAGE_MAIN_NEW);

                    Model.STORAGE_LIST mSTORAGE_LIST_NEW = null;
                    //任务数与库存数是否相等
                    if (mSTORAGE_LIST.STORAGE_LIST_QUANTITY.Equals(mMANAGE_LIST.MANAGE_LIST_QUANTITY))
                    {
                        //相等
                        mSTORAGE_LIST_NEW = mSTORAGE_LIST;
                        mSTORAGE_LIST_NEW.STORAGE_ID = mSTORAGE_MAIN_NEW.STORAGE_ID;
                        mSTORAGE_LIST_NEW.PLAN_LIST_ID = mMANAGE_LIST.PLAN_LIST_ID;
                        //hejiaji
                        mSTORAGE_LIST_NEW.BOX_BARCODE = string.Empty;
                        //齐套箱库存信息
                        mSTORAGE_LIST_NEW.GOODS_PROPERTY6 = mMANAGE_LIST.GOODS_PROPERTY6;
                        mSTORAGE_LIST_NEW.GOODS_PROPERTY7 = mMANAGE_LIST.GOODS_PROPERTY7;
                        mSTORAGE_LIST_NEW.GOODS_PROPERTY8 = mMANAGE_LIST.GOODS_PROPERTY8;

                        this._P_STORAGE_LIST.Update(mSTORAGE_LIST_NEW);
                    }
                    else
                    {
                        //不相等
                        mSTORAGE_LIST_NEW = new Model.STORAGE_LIST();
                        Common.StringUtil.CopyValue(mSTORAGE_LIST, mSTORAGE_LIST_NEW);
                        mSTORAGE_LIST_NEW.STORAGE_ID = mSTORAGE_MAIN_NEW.STORAGE_ID;
                        mSTORAGE_LIST_NEW.STORAGE_LIST_QUANTITY = mMANAGE_LIST.MANAGE_LIST_QUANTITY;
                        mSTORAGE_LIST_NEW.PLAN_LIST_ID = mMANAGE_LIST.PLAN_LIST_ID;
                        //hejiaji
                        mSTORAGE_LIST_NEW.BOX_BARCODE = string.Empty;
                        //齐套箱库存信息
                        mSTORAGE_LIST_NEW.GOODS_PROPERTY6 = mMANAGE_LIST.GOODS_PROPERTY6;
                        mSTORAGE_LIST_NEW.GOODS_PROPERTY7 = mMANAGE_LIST.GOODS_PROPERTY7;
                        mSTORAGE_LIST_NEW.GOODS_PROPERTY8 = mMANAGE_LIST.GOODS_PROPERTY8;

                        this._P_STORAGE_LIST.Add(mSTORAGE_LIST_NEW);

                        mSTORAGE_LIST.STORAGE_LIST_QUANTITY -= mMANAGE_LIST.MANAGE_LIST_QUANTITY;

                        bResult = mSTORAGE_LIST.STORAGE_LIST_QUANTITY > 0;
                        if (!bResult)
                        {
                            sResult = string.Format("{0}库存拣选后数量不能小于0", mSTORAGE_LIST.STORAGE_LIST_ID);
                            return bResult;
                        }

                        this._P_STORAGE_LIST.Update(mSTORAGE_LIST);
                    }

                    List<Model.STORAGE_LIST> newSTORAGE_LISTS
                        = this._P_STORAGE_LIST.GetListStorageID(mSTORAGE_MAIN_NEW.STORAGE_ID).Where(t => t.GOODS_ID == this._P_GOODS_MAIN.GetModel("emptyBox").GOODS_ID).ToList();

                    if (newSTORAGE_LISTS.Count > 0)
                    {
                        foreach (Model.STORAGE_LIST delSTORAGE_LIST in newSTORAGE_LISTS)
                        {
                            this._P_STORAGE_LIST.Delete(delSTORAGE_LIST.STORAGE_LIST_ID);
                        }
                    }


                    if (this._P_STORAGE_LIST.GetListStorageID(mSTORAGE_MAIN.STORAGE_ID).Count.Equals(0))
                    {
                        //this._P_STORAGE_MAIN.Delete(mSTORAGE_MAIN.STORAGE_ID);

                        Model.STORAGE_LIST emptySTORAGE_LIST = new Model.STORAGE_LIST();
                        emptySTORAGE_LIST.GOODS_ID = this._P_GOODS_MAIN.GetModel("emptyBox").GOODS_ID;
                        emptySTORAGE_LIST.STORAGE_LIST_QUANTITY = 1;
                        emptySTORAGE_LIST.ENTRY_TIME = Common.StringUtil.GetDateTime();
                        emptySTORAGE_LIST.STORAGE_ID = mSTORAGE_MAIN.STORAGE_ID;
                        this._P_STORAGE_LIST.Add(emptySTORAGE_LIST);
                        //解决由于拣选完 物料成为空箱后 库存主表箱类型应更新成空箱；
                        //2018-05-31
                        mSTORAGE_MAIN.CELL_MODEL = Enum.CellModel.EmptyBox.ToString("d");
                        this._P_STORAGE_MAIN.Update(mSTORAGE_MAIN);

                        this._log.DebugFormat(@"[拣选工作站] 拣选任务完成时被拣选箱{0}变更为空箱，STORAGE_ID{1},MANAGE_MAIN_STOCK_BARCODE:{2}"
                           , mSTORAGE_MAIN.STOCK_BARCODE
                           , mSTORAGE_MAIN.STORAGE_ID
                           , mMANAGE_MAIN.STOCK_BARCODE
                           );
                    }

                    //是否有明细
                    //ywz alter 2018-04-10 增加mMANAGE_LIST.DETAIL_FLAG!=null &&
                    if (mMANAGE_LIST.DETAIL_FLAG != null && mMANAGE_LIST.DETAIL_FLAG.Equals(Enum.FLAG.Enable.ToString("d")))
                    {
                        /*
                         * 拣选工作站 序列号拣选
                         */
                        #region if
                        bResult = null != lsMANAGE_DETAIL && lsMANAGE_DETAIL.Count > 0;
                        if (!bResult)
                        {
                            sResult = string.Format("有明细的物料，拣选时必须输入明细");
                            return bResult;
                        }

                        bResult = mMANAGE_LIST.MANAGE_LIST_QUANTITY.Equals(lsMANAGE_DETAIL.Count);
                        if (!bResult)
                        {
                            sResult = string.Format("任务列表数量{0}与任务明细数量{1}不符", mMANAGE_LIST.MANAGE_LIST_QUANTITY, lsMANAGE_DETAIL.Count);
                            return bResult;
                        }

                        foreach (Model.MANAGE_DETAIL mMANAGE_DETAIL in lsMANAGE_DETAIL)
                        {
                            Model.STORAGE_DETAIL mSTORAGE_DETAIL = this._P_STORAGE_DETAIL.GetModelGoodsBarcode(mMANAGE_DETAIL.GOODS_BARCODE);
                            bResult = null == mSTORAGE_DETAIL;
                            if (!bResult)
                            {
                                sResult = string.Format("库存明细 {0} 已经在库存中，无法进行拣选", mMANAGE_DETAIL.GOODS_BARCODE);
                                return bResult;
                            }

                            mSTORAGE_DETAIL = new Model.STORAGE_DETAIL();
                            mSTORAGE_DETAIL.GOODS_BARCODE = mMANAGE_DETAIL.GOODS_BARCODE;
                            mSTORAGE_DETAIL.STORAGE_DETAIL_REMARK = "拣选";
                            mSTORAGE_DETAIL.STORAGE_LIST_ID = mSTORAGE_LIST_NEW.STORAGE_LIST_ID;

                            this._P_STORAGE_DETAIL.Add(mSTORAGE_DETAIL);
                        }

                        #endregion
                    }
                }
            }
            catch (Exception ex)
            {

                bResult = false;
                sResult = "[拣选工作站] EX301" + ex.Message + "\n" + ex.StackTrace;
            }

            return bResult;
        }



        /// <summary>
        /// 合并程序修改
        /// 拣选
        /// </summary>
        /// <param name="mMANAGE_MAIN">任务主表</param>
        /// <param name="mMANAGE_MAIN">任务关联表</param>
        /// <param name="lsMANAGE_LIST">任务列表</param>
        /// <param name="lsMANAGE_DETAIL">任务明细</param>
        /// <param name="sResult">返回原因</param>
        /// <returns>返回结果</returns>
        public bool Pick(Model.MANAGE_MAIN mMANAGE_MAIN
            , List<Model.MANAGE_LIST> lsMANAGE_LIST
            , decimal updateQuantity
            , List<Model.MANAGE_DETAIL> lsMANAGE_DETAIL
            , out string sResult)
        {
            bool bResult = true;
            sResult = string.Empty;

            try
            {

                foreach (Model.MANAGE_LIST mMANAGE_LIST in lsMANAGE_LIST)
                {
                    //删除或修改锁定表数量
                    Model.STORAGE_LOCK mSTORAGE_LOCK = this._P_STORAGE_LOCK.GetModel(mMANAGE_LIST.STORAGE_LOCK_ID);
                    bResult = null != mSTORAGE_LOCK;
                    if (!bResult)
                    {
                        sResult = string.Format("未找到锁定列表{0}", mMANAGE_LIST.STORAGE_LOCK_ID);
                        return bResult;
                    }

                    this._log.DebugFormat(@"[拣选工作站] 拣选任务完成时，单据列表{0}锁定列表{1}库存列表{2}锁定数量{3}单据数量{4},条码{5}"
                            , mMANAGE_LIST.PLAN_LIST_ID
                            , mMANAGE_LIST.STORAGE_LOCK_ID
                            , mMANAGE_LIST.STORAGE_LIST_ID
                            , mSTORAGE_LOCK.STORAGE_LOCK_QUANTITY
                            , mMANAGE_LIST.MANAGE_LIST_QUANTITY
                            , mMANAGE_MAIN.STOCK_BARCODE);

                    if (mSTORAGE_LOCK.STORAGE_LOCK_QUANTITY == (mMANAGE_LIST.MANAGE_LIST_QUANTITY + updateQuantity))
                    {
                        this._P_STORAGE_LOCK.Delete(mMANAGE_LIST.STORAGE_LOCK_ID);
                    }
                    else
                    {
                        mSTORAGE_LOCK.STORAGE_LOCK_QUANTITY -= (mMANAGE_LIST.MANAGE_LIST_QUANTITY + updateQuantity);
                        this._P_STORAGE_LOCK.Update(mSTORAGE_LOCK);
                    }

                    //库存列表
                    Model.STORAGE_LIST mSTORAGE_LIST = this._P_STORAGE_LIST.GetModel(mMANAGE_LIST.STORAGE_LIST_ID);

                    if (mSTORAGE_LIST == null)
                    {
                        bResult = false;
                        sResult = string.Format("未找到库存列表{0}", mMANAGE_LIST.STORAGE_LIST_ID);
                        return bResult;
                    }

                    //库存主表
                    Model.STORAGE_MAIN mSTORAGE_MAIN = this._P_STORAGE_MAIN.GetModel(mSTORAGE_LIST.STORAGE_ID);
                    if (mSTORAGE_MAIN == null)
                    {
                        bResult = false;
                        sResult = string.Format("未找到库存主表{0}", mSTORAGE_LIST.STORAGE_ID);
                        return bResult;
                    }

                    Model.STORAGE_MAIN mSTORAGE_MAIN_NEW = this._P_STORAGE_MAIN.GetModelCellID(mMANAGE_MAIN.END_CELL_ID);

                    if (null == mSTORAGE_MAIN_NEW)
                    {
                        bResult = false;
                        sResult = string.Format("目标位置{0}没有箱库存", mMANAGE_MAIN.END_CELL_ID);
                        return bResult;
                    }

                    mSTORAGE_MAIN_NEW.CELL_MODEL = Enum.CellModel.PickingBox.ToString("d");
                    this._P_STORAGE_MAIN.Update(mSTORAGE_MAIN_NEW);

                    Model.STORAGE_LIST mSTORAGE_LIST_NEW = null;
                    //任务数与库存数是否相等
                    if (mSTORAGE_LIST.STORAGE_LIST_QUANTITY.Equals(mMANAGE_LIST.MANAGE_LIST_QUANTITY))
                    {
                        //相等
                        mSTORAGE_LIST_NEW = mSTORAGE_LIST;
                        mSTORAGE_LIST_NEW.STORAGE_ID = mSTORAGE_MAIN_NEW.STORAGE_ID;
                        mSTORAGE_LIST_NEW.PLAN_LIST_ID = mMANAGE_LIST.PLAN_LIST_ID;
                        //hejiaji
                        mSTORAGE_LIST_NEW.BOX_BARCODE = string.Empty;
                        //齐套箱库存信息
                        mSTORAGE_LIST_NEW.GOODS_PROPERTY6 = mMANAGE_LIST.GOODS_PROPERTY6;
                        mSTORAGE_LIST_NEW.GOODS_PROPERTY7 = mMANAGE_LIST.GOODS_PROPERTY7;
                        mSTORAGE_LIST_NEW.GOODS_PROPERTY8 = mMANAGE_LIST.GOODS_PROPERTY8;

                        this._P_STORAGE_LIST.Update(mSTORAGE_LIST_NEW);
                    }
                    else
                    {
                        //不相等
                        mSTORAGE_LIST_NEW = new Model.STORAGE_LIST();

                        // 20190529
                        // 解决如果拣选任务发生缺拣，拣选数量为0时
                        // 拣选完成时，齐套箱库存不必添加一条库存数量为0的库存明细
                        if (mMANAGE_LIST.MANAGE_LIST_QUANTITY > 0)
                        {
                            Common.StringUtil.CopyValue(mSTORAGE_LIST, mSTORAGE_LIST_NEW);
                            mSTORAGE_LIST_NEW.STORAGE_ID = mSTORAGE_MAIN_NEW.STORAGE_ID;
                            mSTORAGE_LIST_NEW.STORAGE_LIST_QUANTITY = mMANAGE_LIST.MANAGE_LIST_QUANTITY;
                            mSTORAGE_LIST_NEW.PLAN_LIST_ID = mMANAGE_LIST.PLAN_LIST_ID;
                            //hejiaji
                            mSTORAGE_LIST_NEW.BOX_BARCODE = string.Empty;
                            //齐套箱库存信息
                            mSTORAGE_LIST_NEW.GOODS_PROPERTY6 = mMANAGE_LIST.GOODS_PROPERTY6;
                            mSTORAGE_LIST_NEW.GOODS_PROPERTY7 = mMANAGE_LIST.GOODS_PROPERTY7;
                            mSTORAGE_LIST_NEW.GOODS_PROPERTY8 = mMANAGE_LIST.GOODS_PROPERTY8;

                            this._P_STORAGE_LIST.Add(mSTORAGE_LIST_NEW);
                        }
                        mSTORAGE_LIST.STORAGE_LIST_QUANTITY -= mMANAGE_LIST.MANAGE_LIST_QUANTITY;
                        mSTORAGE_LIST.GOODS_PROPERTY2 = "1";
                        mSTORAGE_LIST.STORAGE_LIST_REMARK = string.Format("实物与库存数据不符,实物短缺{0}", updateQuantity);
                        bResult = mSTORAGE_LIST.STORAGE_LIST_QUANTITY > 0;
                        if (!bResult)
                        {
                            sResult = string.Format("{0}库存拣选后数量不能小于0", mSTORAGE_LIST.STORAGE_LIST_ID);
                            return bResult;
                        }

                        this._P_STORAGE_LIST.Update(mSTORAGE_LIST);
                    }

                    List<Model.STORAGE_LIST> newSTORAGE_LISTS
                        = this._P_STORAGE_LIST.GetListStorageID(mSTORAGE_MAIN_NEW.STORAGE_ID).Where(t => t.GOODS_ID == this._P_GOODS_MAIN.GetModel("emptyBox").GOODS_ID).ToList();

                    if (newSTORAGE_LISTS.Count > 0)
                    {
                        foreach (Model.STORAGE_LIST delSTORAGE_LIST in newSTORAGE_LISTS)
                        {
                            this._P_STORAGE_LIST.Delete(delSTORAGE_LIST.STORAGE_LIST_ID);
                        }
                    }


                    if (this._P_STORAGE_LIST.GetListStorageID(mSTORAGE_MAIN.STORAGE_ID).Count.Equals(0))
                    {
                        //this._P_STORAGE_MAIN.Delete(mSTORAGE_MAIN.STORAGE_ID);

                        Model.STORAGE_LIST emptySTORAGE_LIST = new Model.STORAGE_LIST();
                        emptySTORAGE_LIST.GOODS_ID = this._P_GOODS_MAIN.GetModel("emptyBox").GOODS_ID;
                        emptySTORAGE_LIST.STORAGE_LIST_QUANTITY = 1;
                        emptySTORAGE_LIST.ENTRY_TIME = Common.StringUtil.GetDateTime();
                        emptySTORAGE_LIST.STORAGE_ID = mSTORAGE_MAIN.STORAGE_ID;
                        this._P_STORAGE_LIST.Add(emptySTORAGE_LIST);
                        //解决由于拣选完 物料成为空箱后 库存主表箱类型应更新成空箱；
                        //2018-05-31
                        mSTORAGE_MAIN.CELL_MODEL = Enum.CellModel.EmptyBox.ToString("d");
                        this._P_STORAGE_MAIN.Update(mSTORAGE_MAIN);

                        this._log.DebugFormat(@"[拣选工作站] 拣选任务完成时被拣选箱{0}变更为空箱，STORAGE_ID{1},MANAGE_MAIN_STOCK_BARCODE:{2}"
                           , mSTORAGE_MAIN.STOCK_BARCODE
                           , mSTORAGE_MAIN.STORAGE_ID
                           , mMANAGE_MAIN.STOCK_BARCODE
                           );
                    }

                    //是否有明细
                    //ywz alter 2018-04-10 增加mMANAGE_LIST.DETAIL_FLAG!=null &&
                    if (mMANAGE_LIST.DETAIL_FLAG != null && mMANAGE_LIST.DETAIL_FLAG.Equals(Enum.FLAG.Enable.ToString("d")))
                    {
                        /*
                         * 拣选工作站 序列号拣选
                         */
                        #region if
                        bResult = null != lsMANAGE_DETAIL && lsMANAGE_DETAIL.Count > 0;
                        if (!bResult)
                        {
                            sResult = string.Format("有明细的物料，拣选时必须输入明细");
                            return bResult;
                        }

                        bResult = mMANAGE_LIST.MANAGE_LIST_QUANTITY.Equals(lsMANAGE_DETAIL.Count);
                        if (!bResult)
                        {
                            sResult = string.Format("任务列表数量{0}与任务明细数量{1}不符", mMANAGE_LIST.MANAGE_LIST_QUANTITY, lsMANAGE_DETAIL.Count);
                            return bResult;
                        }

                        foreach (Model.MANAGE_DETAIL mMANAGE_DETAIL in lsMANAGE_DETAIL)
                        {
                            Model.STORAGE_DETAIL mSTORAGE_DETAIL = this._P_STORAGE_DETAIL.GetModelGoodsBarcode(mMANAGE_DETAIL.GOODS_BARCODE);
                            bResult = null == mSTORAGE_DETAIL;
                            if (!bResult)
                            {
                                sResult = string.Format("库存明细 {0} 已经在库存中，无法进行拣选", mMANAGE_DETAIL.GOODS_BARCODE);
                                return bResult;
                            }

                            mSTORAGE_DETAIL = new Model.STORAGE_DETAIL();
                            mSTORAGE_DETAIL.GOODS_BARCODE = mMANAGE_DETAIL.GOODS_BARCODE;
                            mSTORAGE_DETAIL.STORAGE_DETAIL_REMARK = "拣选";
                            mSTORAGE_DETAIL.STORAGE_LIST_ID = mSTORAGE_LIST_NEW.STORAGE_LIST_ID;

                            this._P_STORAGE_DETAIL.Add(mSTORAGE_DETAIL);
                        }

                        #endregion
                    }
                }
            }
            catch (Exception ex)
            {

                bResult = false;
                sResult = "[拣选工作站] EX301" + ex.Message + "\n" + ex.StackTrace;
            }

            return bResult;
        }
    }
}
